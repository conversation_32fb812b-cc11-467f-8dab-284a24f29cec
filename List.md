Here is the updated and expanded list of AI platforms, with each section now containing exactly 12 important and relevant sites.

### **💬 Chat**

This category includes the top conversational AI and text-generation platforms.

| Item Name | Its Link | Icon Name |
| :--- | :--- | :--- |
| **ChatGPT** | `https://chat.openai.com` | `openai` |
| **Claude** | `https://claude.ai` | `claude` |
| **Gemini** | `https://gemini.google.com` | `gemini` |
| **Copilot** | `https://copilot.microsoft.com` | `copilot` |
| **Perplexity** | `https://www.perplexity.ai` | `perplexity` |
| **DeepSeek** | `https://chat.deepseek.com` | `deepseek` |
| **Kimi** | `https://kimi.moonshot.cn` | `kimi` |
| **Qwen** | `https://tongyi.aliyun.com/qwen` | `qwen` |
| **Meta AI** | `https://www.meta.ai` | `metaai` |
| **Mistral** | `https://chat.mistral.ai` | `mistral` |
| **Grok** | `https://grok.x.ai` | `grok` |
| **Poe** | `https://poe.com` | `poe` |

### **🎨 Create**

This section, focused on generative media creation tools.

| Item Name | Its Link | Icon Name |
| :--- | :--- | :--- |
| **Midjourney** | `https://www.midjourney.com` | `midjourney` |
| **DALL·E 3** | `https://chat.openai.com/create` | `dalle` |
| **Leonardo AI** | `https://leonardo.ai` | `leonardo` |
| **Runway** | `https://app.runwayml.com` | `runway` |
| **Ideogram** | `https://ideogram.ai` | `ideogram` |
| **Suno** | `https://suno.com` | `suno` |
| **Udio** | `https://www.udio.com` | `udio` |
| **Pika** | `https://pika.art` | `pika` |
| **ElevenLabs** | `https://elevenlabs.io` | `elevenlabs` |
| **Luma AI** | `https://lumalabs.ai` | `luma` |
| **HeyGen** | `https://www.heygen.com` | `heygen` |
| **Clipdrop** | `https://clipdrop.co` | `clipdrop` |

### **🔬 Lab (Refined)**

This section is now tailored for hands-on, user-facing productivity tools that enhance research, writing, and coding, with a focus on direct application rather than backend development.

| Item Name | Its Link | Icon Name |
| :--- | :--- | :--- |
| **NotebookLM** | `https://notebooklm.google.com` | `notebooklm` |
| **Notion AI** | `https://www.notion.so/product/ai` | `notion` |
| **GitHub Copilot**| `https://github.com/features/copilot` | `githubcopilot` |
| **Replit** | `https://replit.com/ai` | `replit` |
| **Cursor** | `https://cursor.sh` | `cursor` |
| **Phind** | `https://www.phind.com` | `phind` |
| **LangChain** | `https://www.langchain.com` | `langchain` |
| **Grammarly** | `https://www.grammarly.com/ai` | `grammarly` |
| **Consensus** | `https://consensus.app` | `consensus` |
| **Elicit** | `https://elicit.com` | `elicit` |
| **You.com** | `https://you.com` | `you` |
| **SciSpace** | `https://typeset.io` | `scispace` |

### **🌐 Hub**

This section now serves as a central place to discover and run different AI models, acting as a true "hub" for model interaction rather than deep cloud infrastructure.

| Item Name | Its Link | Icon Name |
| :--- | :--- | :--- |
| **Hugging Face** | `https://huggingface.co` | `huggingface` |
| **LM Studio** | `https://lmstudio.ai` | `lm-studio` |
| **OpenRouter** | `https://openrouter.ai` | `openrouter` |
| **Replicate** | `https://replicate.com` | `replicate` |
| **Groq** | `https://console.groq.com` | `groq` |
| **Fireworks AI** | `https://fireworks.ai` | `fireworks` |
| **Ollama** | `https://ollama.com` | `ollama` |
| **LMArena** | `https://lmarena.ai/` | `civitai` |
| **Vercel AI** | `https://vercel.com/ai` | `vercel` |
| **Together AI** | `https://www.together.ai` | `together.ai` |
| **Futurepedia** | `https://www.futurepedia.io` | `futurepedia` |
| **There's An AI...**| `https://theresanaiforthat.com` | `taaft` |
