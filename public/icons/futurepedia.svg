<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   id="Layer_2"
   data-name="Layer 2"
   viewBox="0 0 204.16 42.11"
   version="1.1"
   sodipodi:docname="futurepedia.svg"
   inkscape:version="1.3 (0e150ed, 2023-07-21)"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg">
  <sodipodi:namedview
     id="namedview17"
     pagecolor="#ffffff"
     bordercolor="#000000"
     borderopacity="0.25"
     inkscape:showpageshadow="2"
     inkscape:pageopacity="0.0"
     inkscape:pagecheckerboard="0"
     inkscape:deskcolor="#d1d1d1"
     inkscape:zoom="8.2015518"
     inkscape:cx="102.05386"
     inkscape:cy="21.093569"
     inkscape:window-width="2992"
     inkscape:window-height="1623"
     inkscape:window-x="1544"
     inkscape:window-y="33"
     inkscape:window-maximized="0"
     inkscape:current-layer="Layer_2" />
  <defs
     id="defs1">
    <style
       id="style1">&#10;      .cls-1 {&#10;        fill: #273753;&#10;      }&#10;&#10;      .cls-1, .cls-2, .cls-3 {&#10;        stroke-width: 0px;&#10;      }&#10;&#10;      .cls-2 {&#10;        fill: #62a7db;&#10;      }&#10;&#10;      .cls-3 {&#10;        fill: #fff;&#10;      }&#10;    </style>
    <filter
       style="color-interpolation-filters:sRGB;"
       inkscape:label="Duochrome"
       id="filter21"
       x="0"
       y="0"
       width="1"
       height="1">
      <feColorMatrix
         type="luminanceToAlpha"
         result="colormatrix1"
         id="feColorMatrix17" />
      <feFlood
         flood-opacity="1"
         flood-color="rgb(0,0,0)"
         result="flood1"
         id="feFlood17" />
      <feComposite
         in2="colormatrix1"
         operator="out"
         result="composite1"
         id="feComposite17" />
      <feFlood
         flood-opacity="1"
         flood-color="rgb(255,255,255)"
         result="flood2"
         id="feFlood18" />
      <feComposite
         in2="colormatrix1"
         result="composite2"
         operator="in"
         id="feComposite18" />
      <feComposite
         in="composite2"
         in2="composite1"
         k2="1"
         k3="1"
         operator="arithmetic"
         result="composite3"
         id="feComposite19" />
      <feColorMatrix
         in="composite3"
         type="matrix"
         values="2 -1 0 0 0 0 2 -1 0 0 -1 0 2 0 0 0 0 0 1 0 "
         result="colormatrix2"
         id="feColorMatrix19" />
      <feComposite
         in="colormatrix2"
         in2="composite3"
         operator="arithmetic"
         k2="0"
         result="composite4"
         id="feComposite20" />
      <feBlend
         in="composite4"
         in2="composite3"
         mode="normal"
         result="blend"
         id="feBlend20" />
      <feComposite
         in2="SourceGraphic"
         operator="in"
         id="feComposite21"
         result="fbSourceGraphic" />
      <feColorMatrix
         result="fbSourceGraphicAlpha"
         in="fbSourceGraphic"
         values="0 0 0 -1 0 0 0 0 -1 0 0 0 0 -1 0 0 0 0 1 0"
         id="feColorMatrix21" />
      <feColorMatrix
         id="feColorMatrix22"
         type="luminanceToAlpha"
         result="colormatrix1"
         in="fbSourceGraphic" />
      <feFlood
         id="feFlood22"
         flood-opacity="1"
         flood-color="rgb(0,0,0)"
         result="flood1" />
      <feComposite
         id="feComposite22"
         in2="colormatrix1"
         operator="out"
         result="composite1" />
      <feFlood
         id="feFlood23"
         flood-opacity="1"
         flood-color="rgb(255,255,255)"
         result="flood2" />
      <feComposite
         id="feComposite23"
         in2="colormatrix1"
         result="composite2"
         operator="in" />
      <feComposite
         id="feComposite24"
         in="composite2"
         in2="composite1"
         k2="1"
         k3="1"
         operator="arithmetic"
         result="composite3" />
      <feColorMatrix
         id="feColorMatrix24"
         in="composite3"
         type="matrix"
         values="2 -1 0 0 0 0 2 -1 0 0 -1 0 2 0 0 0 0 0 1 0 "
         result="colormatrix2" />
      <feComposite
         id="feComposite25"
         in="colormatrix2"
         in2="composite3"
         operator="arithmetic"
         k2="0"
         result="composite4" />
      <feBlend
         id="feBlend25"
         in="composite4"
         in2="composite3"
         mode="normal"
         result="blend" />
      <feComposite
         id="feComposite26"
         in2="fbSourceGraphic"
         operator="in" />
    </filter>
    <filter
       style="color-interpolation-filters:sRGB;"
       inkscape:label="Duochrome"
       id="filter31"
       x="0"
       y="0"
       width="1"
       height="1">
      <feColorMatrix
         type="luminanceToAlpha"
         result="colormatrix1"
         id="feColorMatrix26" />
      <feFlood
         flood-opacity="1"
         flood-color="rgb(0,0,0)"
         result="flood1"
         id="feFlood26" />
      <feComposite
         in2="colormatrix1"
         operator="out"
         result="composite1"
         id="feComposite27" />
      <feFlood
         flood-opacity="1"
         flood-color="rgb(255,255,255)"
         result="flood2"
         id="feFlood27" />
      <feComposite
         in2="colormatrix1"
         result="composite2"
         operator="in"
         id="feComposite28" />
      <feComposite
         in="composite2"
         in2="composite1"
         k2="1"
         k3="1"
         operator="arithmetic"
         result="composite3"
         id="feComposite29" />
      <feColorMatrix
         in="composite3"
         type="matrix"
         values="2 -1 0 0 0 0 2 -1 0 0 -1 0 2 0 0 0 0 0 1 0 "
         result="colormatrix2"
         id="feColorMatrix29" />
      <feComposite
         in="colormatrix2"
         in2="composite3"
         operator="arithmetic"
         k2="0"
         result="composite4"
         id="feComposite30" />
      <feBlend
         in="composite4"
         in2="composite3"
         mode="normal"
         result="blend"
         id="feBlend30" />
      <feComposite
         in2="SourceGraphic"
         operator="in"
         id="feComposite31" />
    </filter>
  </defs>
  <g
     id="Layer_1-2"
     data-name="Layer 1">
    <circle
       class="cls-1"
       cx="21.05"
       cy="21.05"
       r="21.05"
       id="circle11"
       style="filter:url(#filter21)" />
    <path
       class="cls-3"
       d="M13.34,24.87c0,.42.34.76.76.76h0c2.81,0,5.08-2.27,5.08-5.08v-10.67c0-.42-.34-.76-.76-.76h0c-2.81,0-5.08,2.27-5.08,5.08v10.67Z"
       id="path12" />
    <path
       class="cls-3"
       d="M13.34,14.2c0-2.81,2.27-5.08,5.08-5.08h9.53c1.75,0,3.18,1.42,3.18,3.18s-1.42,3.18-3.18,3.18h-13.34c-.7,0-1.27-.57-1.27-1.27h0Z"
       id="path13" />
    <path
       class="cls-2"
       d="M13.34,24.37c0-2.81,2.27-5.08,5.08-5.08h5.21c1.75,0,3.18,1.42,3.18,3.18h0c0,1.75-1.42,3.18-3.18,3.18h-10.29v-1.27Z"
       id="path14"
       style="filter:url(#filter31)" />
    <path
       class="cls-3"
       d="M13.34,29.83c0,1.75,1.42,3.18,3.18,3.18h0c1.75,0,3.18-1.42,3.18-3.18h0c0-1.75-1.42-3.18-3.18-3.18h-3.18v3.18Z"
       id="path15" />
  </g>
</svg>
