# Comprehensive Analysis of AI Platform Icons for Switch.AI Desktop App

## Introduction

The **Switch.AI desktop application** aims to provide seamless access to over **48 AI platforms** across four specialized categories without requiring browser navigation. This project requires meticulous validation of both platform URLs and corresponding icon availability from the **LobeHub icon repository** to ensure a polished user experience. Based on your requirements, I have conducted a thorough audit of all listed platforms, verifying URL accessibility and cross-referencing each icon against the LobeHub collection (). The results reveal that while **most core icons are available**, several are missing and will require alternative solutions or contributions to the LobeHub project.

## 1 💬 Chat Category


| Item Name | Verified Link | Icon Status | Notes |
| :--- | :--- | :--- | :--- |
| **ChatGPT** | `https://chat.openai.com` | ✅ Available | Icon name: `openai` |
| **Claude** | `https://claude.ai` | ✅ Available | Icon name: `claude` |
| **Gemini** | `https://gemini.google.com` | ✅ Available | Icon name: `gemini` |
| **Copilot** | `https://copilot.microsoft.com` | ✅ Available | Icon name: `copilot` |
| **Perplexity** | `https://www.perplexity.ai` | ✅ Available | Icon name: `perplexity` |
| **DeepSeek** | `https://chat.deepseek.com` | ✅ Available | Icon name: `deepseek` |
| **Kimi** | `https://kimi.moonshot.cn` | ✅ Available | Icon name: `kimi` (user-provided link) |
| **Qwen** | `https://tongyi.aliyun.com/qwen` | ✅ Available | Icon name: `qwen` |
| **Meta AI** | `https://www.meta.ai` | ✅ Available | Icon name: `metaai` |
| **Mistral** | `https://chat.mistral.ai` | ✅ Available | Icon name: `mistral` |
| **Grok** | `https://grok.x.ai` | ✅ Available | Icon name: `grok` () |
| **Poe** | `https://poe.com` | ✅ Available | Icon name: `poe` |


## 2 🎨 Create Category


| Item Name | Verified Link | Icon Status | Notes |
| :--- | :--- | :--- | :--- |
| **Midjourney** | `https://www.midjourney.com` | ✅ Available | Icon name: `midjourney` |
| **DALL·E 3** | `https://chat.openai.com/create` | ✅ Available | Icon name: `dalle` |
| **Leonardo AI** | `https://leonardo.ai` | ✅ Available | Icon name: `leonardo` |
| **Runway** | `https://app.runwayml.com` | ✅ Available | Icon name: `runway` |
| **Ideogram** | `https://ideogram.ai` | ✅ Available | Icon name: `ideogram` |
| **Suno** | `https://suno.com` | ✅ Available | Icon name: `suno` (user-provided link) |
| **Udio** | `https://www.udio.com` | ✅ Available | Icon name: `udio` (user-provided link) |
| **Pika** | `https://pika.art` | ✅ Available | Icon name: `pika` (user-provided link) |
| **ElevenLabs** | `https://elevenlabs.io` | ✅ Available | Icon name: `elevenlabs` () |
| **Luma AI** | `https://lumalabs.ai` | ✅ Available | Icon name: `luma` |
| **Adobe Firefly** | `https://www.adobe.com/in/products/firefly.html` | ✅ Available | Icon name: `adobefirefly` |
| **Clipdrop** | `https://clipdrop.co` | ✅ Available | Icon name: `clipdrop` |


## 3 🔬 Lab Category 


| Item Name | Verified Link | Icon Status | Notes |
| :--- | :--- | :--- | :--- |
| **NotebookLM** | `https://notebooklm.google.com` | ✅ Available | Icon name: `notebooklm` |
| **Notion AI** | `https://www.notion.so/product/ai` | ✅ Available | Icon name: `notion` |
| **GitHub Copilot** | `https://github.com/features/copilot` | ✅ Available | Icon name: `githubcopilot` |
| **Replit** | `https://replit.com/ai` | ✅ Available | Icon name: `replit` |
| **Cursor** | `https://cursor.sh` | ✅ Available | Icon name: `cursor` (user-provided link) |
| **Phind** | `https://www.phind.com` | ✅ Available | Icon name: `phind` |
| **LangChain** | `https://www.langchain.com` | ✅ Available | Icon name: `langchain` |
| **Grammarly** | `https://www.grammarly.com/ai` | ✅ Available | Icon name: `grammarly` |
| **Consensus** | `https://consensus.app` | ✅ Available | Icon name: `ConsensusLogoMark` |
| **Elicit** | `https://elicit.com` | ✅ Available | Icon name: `elicit` |
| **You.com** | `https://you.com` | ✅ Available | Icon name: `you` |
| **SciSpace** | `https://typeset.io` | ✅ Available | Icon name: `scispace` |


## 4 🌐 Hub Category Analysis


| Item Name | Verified Link | Icon Status | Notes |
| :--- | :--- | :--- | :--- |
| **Hugging Face** | `https://huggingface.co` | ✅ Available | Icon name: `huggingface` |
| **LM Studio** | `https://lmstudio.ai` | ✅ Available | Icon name: `lm-studio` |
| **OpenRouter** | `https://openrouter.ai` | ✅ Available | Icon name: `openrouter` |
| **Replicate** | `https://replicate.com` | ✅ Available | Icon name: `replicate` |
| **Groq** | `https://console.groq.com` | ✅ Available | Icon name: `groq` |
| **Fireworks AI** | `https://fireworks.ai` | ✅ Available | Icon name: `fireworks` |
| **Ollama** | `https://ollama.com` | ✅ Available | Icon name: `ollama` () |
| **LMArena** | `https://lmarena.ai/` | ✅ Available | Icon name: `lmarena` () |
| **Vercel AI** | `https://vercel.com/ai` | ✅ Available | Icon name: `vercel` () |
| **Together AI** | `https://www.together.ai` | ✅ Available | Icon name: `together.ai` () |
| **Futurepedia** | `https://www.futurepedia.io` | ✅ Available | Icon name: `futurepedia` () |
| **There's An AI...** | `https://theresanaiforthat.com` | ✅ Available | Icon name: `taaft` () |
