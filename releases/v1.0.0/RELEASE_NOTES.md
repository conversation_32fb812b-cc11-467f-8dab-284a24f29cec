# Switch AI Browser v1.0.0 Release Notes

## 🎉 Major Update: 48 AI Platforms & Enhanced UI

This release implements the complete Update Plan 01, expanding Switch AI Browser from 40 to 48 AI platforms with significant UI improvements and enhanced icon management.

## ✨ What's New

### 🚀 **Expanded Platform Support (40 → 48 platforms)**
- **💬 Chat Mode (12 platforms)**: ChatGPT, Claude, Gemini, Copilot, Perplexity, DeepSeek, Kimi, Qwen, Meta AI, Mistral, Grok, Poe
- **🎨 Create Mode (12 platforms)**: Midjourney, DALL·E, Runway, Stable Diffusion, Leonardo AI, Ideogram, Suno, Udio, Pika, HeyGen, Synthesia, ElevenLabs
- **🔬 Lab Mode (12 platforms)**: NotebookLM, Consensus, Elicit, SciSpace, Grammarly AI, You.com, Notion AI, GitHub Copilot, Replit, Codeium, Cursor, Phind
- **🌐 Hub Mode (12 platforms)**: Hugging Face, OpenRouter, Replicate, Groq, Together AI, Fireworks AI, Ollama, Civitai, LangChain, Vercel AI, Futurepedia, There's An AI For That

### 🎨 **Enhanced Icon System**
- **Dynamic Icon Switching**: Selected platforms now show full-color icons, unselected show grayscale
- **96 High-Quality Icons**: Both standard (.svg) and color (.color.svg) versions for each platform
- **Improved Performance**: File-based switching instead of CSS filters

### 🖥️ **Redesigned User Interface**
- **Integrated Top Bar**: Mode navigation now integrated into the top bar for cleaner design
- **Removed Floating Switcher**: Eliminated the separate MasterSwitcher component
- **Better Layout**: More conventional and intuitive UI structure
- **Updated Branding**: Window title changed to "Switch AI Browser"

### 🐧 **Linux Support**
- **Complete Build Instructions**: Added comprehensive Linux build documentation
- **System Dependencies**: Detailed setup guide for Debian/Ubuntu systems
- **Cross-Platform Ready**: Now officially supports macOS, Windows, and Linux

## 📦 Available Downloads

### macOS
- **DMG Installer**: `Switch_AI_Browser_v1.0.0_macOS.dmg` (3.5 MB)
  - Drag-and-drop installation
  - Works on both Intel and Apple Silicon Macs
  
- **App Bundle**: `Switch.AI.app` (8.4 MB)
  - Direct application bundle
  - Can be placed anywhere in Applications folder
  
- **Standalone Binary**: `switch-ai-macos-binary` (8.7 MB)
  - Command-line executable
  - No installation required

### Windows & Linux
- Build from source using the provided instructions
- All dependencies and build steps documented in README.md

## 🔧 Technical Improvements

- **Cleaner Codebase**: Removed redundant components and improved code organization
- **Better Type Safety**: Enhanced TypeScript definitions and imports
- **Optimized Build**: Improved build process and bundle size
- **Updated Dependencies**: Latest versions of Tauri, React, and other core libraries

## 📋 Verification

All release files include SHA-256 checksums in `checksums.txt`:
```
40e2d857b27fbdcad2188de7e915965802ae6a20dbe64fab23bc27f99c854484  Switch_AI_Browser_v1.0.0_macOS.dmg
cc1ffe7a0d5b94c131bd44db4433771887c39d3cf5b7cfece62a3b9b293441fa  switch-ai-macos-binary
```

## 🚀 Installation

### macOS DMG (Recommended)
1. Download `Switch_AI_Browser_v1.0.0_macOS.dmg`
2. Double-click to mount the disk image
3. Drag Switch AI Browser to Applications folder
4. Launch from Applications or Spotlight

### macOS App Bundle
1. Download and extract `Switch.AI.app`
2. Move to Applications folder
3. Right-click and select "Open" (first time only due to Gatekeeper)

## 🔄 Upgrade Notes

- This is a major update with significant UI changes
- All user preferences and settings are preserved
- New platforms will appear automatically in their respective modes
- Icon system has been completely redesigned for better performance

## 🐛 Known Issues

- Some placeholder icons are used for newer platforms (will be updated in future releases)
- DMG creation occasionally fails during build (app bundle always works)

## 🙏 Acknowledgments

Built with love using:
- **Tauri 2.7** - Cross-platform app framework
- **React 18** - UI framework  
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **Vite** - Build tool

---

**Note**: This is a hobby project for friends. No signing, no app store, no CI/CD. Just build and share! 🎉

For build instructions and source code, see the main repository README.md.
