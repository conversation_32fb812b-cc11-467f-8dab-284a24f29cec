import { Platform } from '../types';

export const platforms: Platform[] = [
  /* 💬 Chat */
  { id: 'chatgpt', mode: 'chat', name: 'ChatGPT', url: 'https://chat.openai.com', iconFile: 'chatgpt.svg' },
  { id: 'claude', mode: 'chat', name: '<PERSON>', url: 'https://claude.ai', iconFile: 'claude.svg' },
  { id: 'gemini', mode: 'chat', name: '<PERSON>', url: 'https://gemini.google.com', iconFile: 'gemini.svg' },
  { id: 'copilot', mode: 'chat', name: 'Copilot', url: 'https://copilot.microsoft.com', iconFile: 'copilot.svg' },
  { id: 'perplexity', mode: 'chat', name: 'Perplexity', url: 'https://perplexity.ai', iconFile: 'perplexity.svg' },
  { id: 'deepseek', mode: 'chat', name: 'DeepSeek', url: 'https://chat.deepseek.com', iconFile: 'deepseek.svg' },
  { id: 'kimi', mode: 'chat', name: '<PERSON><PERSON>', url: 'https://kimi.moonshot.cn', iconFile: 'kimi.svg' },
  { id: 'qwen', mode: 'chat', name: 'Qwen', url: 'https://qwen.aliyun.com', iconFile: 'qwen.svg' },
  { id: 'meta-ai', mode: 'chat', name: 'Meta AI', url: 'https://ai.meta.com', iconFile: 'metaai.svg' },
  { id: 'mistral', mode: 'chat', name: 'Mistral Chat', url: 'https://chat.mistral.ai', iconFile: 'mistral.svg' },
  { id: 'grok', mode: 'chat', name: 'Grok', url: 'https://grok.x.ai', iconFile: 'grok.svg' },
  { id: 'poe', mode: 'chat', name: 'Poe', url: 'https://poe.com', iconFile: 'poe.svg' },

  /* 🎨 Create */
  { id: 'midjourney', mode: 'create', name: 'Midjourney', url: 'https://midjourney.com', iconFile: 'midjourney.svg' },
  { id: 'dalle', mode: 'create', name: 'DALL·E', url: 'https://openai.com/dall-e-3', iconFile: 'dalle.svg' },
  { id: 'runway', mode: 'create', name: 'Runway', url: 'https://runwayml.com', iconFile: 'runway.svg' },
  { id: 'stablediff', mode: 'create', name: 'Stable Diffusion', url: 'https://stablediffusionweb.com', iconFile: 'sd.svg' },
  { id: 'leonardo', mode: 'create', name: 'Leonardo AI', url: 'https://leonardo.ai', iconFile: 'leonardo.svg' },
  { id: 'ideogram', mode: 'create', name: 'Ideogram', url: 'https://ideogram.ai', iconFile: 'ideogram.svg' },
  { id: 'suno', mode: 'create', name: 'Suno', url: 'https://suno.ai', iconFile: 'suno.svg' },
  { id: 'udio', mode: 'create', name: 'Udio', url: 'https://www.udio.com', iconFile: 'udio.svg' },
  { id: 'pika', mode: 'create', name: 'Pika', url: 'https://pika.art', iconFile: 'pika.svg' },
  { id: 'heygen', mode: 'create', name: 'HeyGen', url: 'https://www.heygen.com', iconFile: 'heygen.svg' },
  { id: 'synthesia', mode: 'create', name: 'Synthesia', url: 'https://www.synthesia.io', iconFile: 'synthesia.svg' },
  { id: 'elevenlabs', mode: 'create', name: 'ElevenLabs', url: 'https://elevenlabs.io', iconFile: 'elevenlabs.svg' },

  /* 🔬 Lab */
  { id: 'notebooklm', mode: 'lab', name: 'NotebookLM', url: 'https://notebooklm.google.com', iconFile: 'notebooklm.svg' },
  { id: 'consensus', mode: 'lab', name: 'Consensus', url: 'https://consensus.app', iconFile: 'consensus.svg' },
  { id: 'elicit', mode: 'lab', name: 'Elicit', url: 'https://elicit.org', iconFile: 'elicit.svg' },
  { id: 'scispace', mode: 'lab', name: 'SciSpace', url: 'https://scispace.com', iconFile: 'scispace.svg' },
  { id: 'grammarly', mode: 'lab', name: 'Grammarly AI', url: 'https://www.grammarly.com/ai', iconFile: 'grammarly.svg' },
  { id: 'you', mode: 'lab', name: 'You.com', url: 'https://you.com', iconFile: 'you.svg' },
  { id: 'notion-ai', mode: 'lab', name: 'Notion AI', url: 'https://www.notion.so/product/ai', iconFile: 'notion.svg' },
  { id: 'gh-copilot', mode: 'lab', name: 'GitHub Copilot', url: 'https://github.com/features/copilot', iconFile: 'github.svg' },
  { id: 'replit', mode: 'lab', name: 'Replit', url: 'https://replit.com', iconFile: 'replit.svg' },
  { id: 'codeium', mode: 'lab', name: 'Codeium', url: 'https://codeium.com', iconFile: 'codeium.svg' },
  { id: 'cursor', mode: 'lab', name: 'Cursor', url: 'https://cursor.sh', iconFile: 'cursor.svg' },
  { id: 'phind', mode: 'lab', name: 'Phind', url: 'https://www.phind.com', iconFile: 'phind.svg' },

  /* 🌐 Hub */
  { id: 'huggingface', mode: 'hub', name: 'Hugging Face', url: 'https://huggingface.co', iconFile: 'huggingface.svg' },
  { id: 'openrouter', mode: 'hub', name: 'OpenRouter', url: 'https://openrouter.ai', iconFile: 'openrouter.svg' },
  { id: 'replicate', mode: 'hub', name: 'Replicate', url: 'https://replicate.com', iconFile: 'replicate.svg' },
  { id: 'groq', mode: 'hub', name: 'Groq', url: 'https://groq.com', iconFile: 'groq.svg' },
  { id: 'together', mode: 'hub', name: 'Together AI', url: 'https://www.together.ai', iconFile: 'together.svg' },
  { id: 'fireworks', mode: 'hub', name: 'Fireworks AI', url: 'https://fireworks.ai', iconFile: 'fireworks.svg' },
  { id: 'ollama', mode: 'hub', name: 'Ollama', url: 'https://ollama.com', iconFile: 'ollama.svg' },
  { id: 'civitai', mode: 'hub', name: 'Civitai', url: 'https://civitai.com', iconFile: 'civitai.svg' },
  { id: 'langchain', mode: 'hub', name: 'LangChain', url: 'https://www.langchain.com', iconFile: 'langchain.svg' },
  { id: 'vercel', mode: 'hub', name: 'Vercel AI', url: 'https://vercel.com/solutions/ai', iconFile: 'vercel.svg' },
  { id: 'futurepedia', mode: 'hub', name: 'Futurepedia', url: 'https://www.futurepedia.io', iconFile: 'futurepedia.svg' },
  { id: 'taaft', mode: 'hub', name: "There's An AI For That", url: 'https://theresanaiforthat.com', iconFile: 'taaft.svg' }
];
