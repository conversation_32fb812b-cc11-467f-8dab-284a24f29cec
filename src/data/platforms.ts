import { Platform } from '../types';

export const platforms: Platform[] = [
  /* 💬 Chat */
  { id: 'chatgpt', mode: 'chat', name: 'ChatGPT', url: 'https://chat.openai.com', iconFile: 'openai.svg' },
  { id: 'claude', mode: 'chat', name: '<PERSON>', url: 'https://claude.ai', iconFile: 'claude.svg' },
  { id: 'gemini', mode: 'chat', name: '<PERSON>', url: 'https://gemini.google.com', iconFile: 'gemini.svg' },
  { id: 'copilot', mode: 'chat', name: 'Copilot', url: 'https://copilot.microsoft.com', iconFile: 'copilot.svg' },
  { id: 'perplexity', mode: 'chat', name: 'Perplexity', url: 'https://www.perplexity.ai', iconFile: 'perplexity.svg' },
  { id: 'deepseek', mode: 'chat', name: '<PERSON><PERSON>eek', url: 'https://chat.deepseek.com', iconFile: 'deepseek.svg' },
  { id: 'kimi', mode: 'chat', name: '<PERSON><PERSON>', url: 'https://kimi.moonshot.cn', iconFile: 'kimi.svg' },
  { id: 'qwen', mode: 'chat', name: 'Qwen', url: 'https://tongyi.aliyun.com/qwen', iconFile: 'qwen.svg' },
  { id: 'meta-ai', mode: 'chat', name: 'Meta AI', url: 'https://www.meta.ai', iconFile: 'metaai.svg' },
  { id: 'mistral', mode: 'chat', name: 'Mistral', url: 'https://chat.mistral.ai', iconFile: 'mistral.svg' },
  { id: 'grok', mode: 'chat', name: 'Grok', url: 'https://grok.x.ai', iconFile: 'grok.svg' },
  { id: 'poe', mode: 'chat', name: 'Poe', url: 'https://poe.com', iconFile: 'poe.svg' },

  /* 🎨 Create */
  { id: 'midjourney', mode: 'create', name: 'Midjourney', url: 'https://www.midjourney.com', iconFile: 'midjourney.svg' },
  { id: 'dalle', mode: 'create', name: 'DALL·E 3', url: 'https://chat.openai.com/create', iconFile: 'dalle.svg' },
  { id: 'leonardo', mode: 'create', name: 'Leonardo AI', url: 'https://leonardo.ai', iconFile: 'leonardo.svg' },
  { id: 'runway', mode: 'create', name: 'Runway', url: 'https://app.runwayml.com', iconFile: 'runway.svg' },
  { id: 'ideogram', mode: 'create', name: 'Ideogram', url: 'https://ideogram.ai', iconFile: 'ideogram.svg' },
  { id: 'suno', mode: 'create', name: 'Suno', url: 'https://suno.com', iconFile: 'suno.svg' },
  { id: 'udio', mode: 'create', name: 'Udio', url: 'https://www.udio.com', iconFile: 'udio.svg' },
  { id: 'pika', mode: 'create', name: 'Pika', url: 'https://pika.art', iconFile: 'pika.svg' },
  { id: 'elevenlabs', mode: 'create', name: 'ElevenLabs', url: 'https://elevenlabs.io', iconFile: 'elevenlabs.svg' },
  { id: 'luma', mode: 'create', name: 'Luma AI', url: 'https://lumalabs.ai', iconFile: 'luma.svg' },
  { id: 'adobefirefly', mode: 'create', name: 'Adobe Firefly', url: 'https://www.adobe.com/in/products/firefly.html', iconFile: 'adobefirefly.svg' },
  { id: 'clipdrop', mode: 'create', name: 'Clipdrop', url: 'https://clipdrop.co', iconFile: 'clipdrop.svg' },

  /* 🔬 Lab */
  { id: 'notebooklm', mode: 'lab', name: 'NotebookLM', url: 'https://notebooklm.google.com', iconFile: 'notebooklm.svg' },
  { id: 'notion-ai', mode: 'lab', name: 'Notion AI', url: 'https://www.notion.so/product/ai', iconFile: 'notion.svg' },
  { id: 'gh-copilot', mode: 'lab', name: 'GitHub Copilot', url: 'https://github.com/features/copilot', iconFile: 'githubcopilot.svg' },
  { id: 'replit', mode: 'lab', name: 'Replit', url: 'https://replit.com/ai', iconFile: 'replit.svg' },
  { id: 'cursor', mode: 'lab', name: 'Cursor', url: 'https://cursor.sh', iconFile: 'cursor.svg' },
  { id: 'phind', mode: 'lab', name: 'Phind', url: 'https://www.phind.com', iconFile: 'phind.svg' },
  { id: 'langchain', mode: 'lab', name: 'LangChain', url: 'https://www.langchain.com', iconFile: 'langchain.svg' },
  { id: 'grammarly', mode: 'lab', name: 'Grammarly', url: 'https://www.grammarly.com/ai', iconFile: 'grammarly.svg' },
  { id: 'consensus', mode: 'lab', name: 'Consensus', url: 'https://consensus.app', iconFile: 'ConsensusLogoMark.svg' },
  { id: 'elicit', mode: 'lab', name: 'Elicit', url: 'https://elicit.com', iconFile: 'elicit.svg' },
  { id: 'you', mode: 'lab', name: 'You.com', url: 'https://you.com', iconFile: 'You.com.svg' },
  { id: 'scispace', mode: 'lab', name: 'SciSpace', url: 'https://typeset.io', iconFile: 'scispace.svg' },

  /* 🌐 Hub */
  { id: 'huggingface', mode: 'hub', name: 'Hugging Face', url: 'https://huggingface.co', iconFile: 'huggingface.svg' },
  { id: 'lmstudio', mode: 'hub', name: 'LM Studio', url: 'https://lmstudio.ai', iconFile: 'lmstudio.svg' },
  { id: 'openrouter', mode: 'hub', name: 'OpenRouter', url: 'https://openrouter.ai', iconFile: 'openrouter.svg' },
  { id: 'replicate', mode: 'hub', name: 'Replicate', url: 'https://replicate.com', iconFile: 'replicate.svg' },
  { id: 'groq', mode: 'hub', name: 'Groq', url: 'https://console.groq.com', iconFile: 'groq.svg' },
  { id: 'fireworks', mode: 'hub', name: 'Fireworks AI', url: 'https://fireworks.ai', iconFile: 'fireworks.svg' },
  { id: 'ollama', mode: 'hub', name: 'Ollama', url: 'https://ollama.com', iconFile: 'ollama.svg' },
  { id: 'lmarena', mode: 'hub', name: 'LMArena', url: 'https://lmarena.ai/', iconFile: 'lmarena.svg' },
  { id: 'vercel', mode: 'hub', name: 'Vercel AI', url: 'https://vercel.com/ai', iconFile: 'vercel.svg' },
  { id: 'together', mode: 'hub', name: 'Together AI', url: 'https://www.together.ai', iconFile: 'together.svg' },
  { id: 'futurepedia', mode: 'hub', name: 'Futurepedia', url: 'https://www.futurepedia.io', iconFile: 'futurepedia.svg' },
  { id: 'taaft', mode: 'hub', name: "There's An AI For That", url: 'https://theresanaiforthat.com', iconFile: 'taaft.svg' }
];
