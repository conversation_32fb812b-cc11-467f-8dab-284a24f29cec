import { useState } from 'react';

export const Settings = () => {
  const [customJson, setCustomJson] = useState('');

  const importPlatforms = async () => {
    // For now, just a placeholder
    console.log('Import platforms functionality');
  };

  return (
    <div className="p-8 space-y-6">
      <h2 className="text-2xl font-bold">Settings</h2>

      <section>
        <h3 className="font-semibold mb-2">Appearance</h3>
        <label className="flex items-center space-x-2">
          <input type="checkbox" className="rounded" />
          <span>Dark mode (auto)</span>
        </label>
      </section>

      <section>
        <h3 className="font-semibold mb-2">Keyboard Shortcuts</h3>
        <p className="text-sm text-textSecondary">
          Ctrl+Shift+C → Chat &nbsp;&nbsp; Ctrl+Shift+R → Create …
        </p>
      </section>

      <section>
        <h3 className="font-semibold mb-2">Custom Platforms</h3>
        <button 
          onClick={importPlatforms} 
          className="px-4 py-2 bg-accentStart hover:bg-accentEnd rounded transition-colors"
        >
          Import JSON
        </button>
        <textarea
          value={customJson}
          onChange={(e) => setCustomJson(e.target.value)}
          rows={4}
          className="mt-2 w-full bg-bgSecondary border border-border rounded p-2 text-sm"
          placeholder="Drag or paste your platforms.json here"
        />
      </section>

      <section>
        <button className="px-4 py-2 bg-border hover:bg-border/80 rounded transition-colors">
          Clear Cache & Reload
        </button>
      </section>
    </div>
  );
};
