import { platforms } from '../data/platforms';
import { useAppStore } from '../store';

export const Sidebar = () => {
  const { activeMode, activePlatform, setPlatform } = useAppStore();
  const list = platforms.filter((p) => p.mode === activeMode);

  return (
    <aside className="w-sidebar bg-bgSecondary border-r border-border flex flex-col items-center py-4 space-y-3">
      {list.map((p) => {
        const isSelected = activePlatform?.id === p.id;

        return (
          <div
            key={p.id}
            onClick={() => setPlatform(p)}
            className={`group relative w-icon h-icon rounded-xl border cursor-pointer transition-all duration-200 flex items-center justify-center
              ${
                isSelected
                  ? 'border-accentEnd scale-105'
                  : 'border-border hover:border-border/50 hover:scale-105'
              }`}
          >
            <img
              src={`/icons/${p.iconFile}`}
              alt={p.name}
              className="w-7 h-7"
            />
            <span className="absolute left-full ml-2 top-1/2 -translate-y-1/2 bg-bgSecondary text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap border border-border z-30">
              {p.name}
            </span>
          </div>
        );
      })}
    </aside>
  );
};
