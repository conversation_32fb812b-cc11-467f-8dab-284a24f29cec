import { WebviewWindow } from '@tauri-apps/api/webviewWindow';
import { useEffect } from 'react';
import { useAppStore } from '../store';

export const WebView = () => {
  const { activePlatform } = useAppStore();

  useEffect(() => {
    if (!activePlatform) return;

    const webview = new WebviewWindow(activePlatform.id, {
      url: activePlatform.url,
      title: activePlatform.name,
      width: 1200,
      height: 800,
      resizable: true,
      fullscreen: false,
    });

    return () => {
      webview.close().catch(() => {
        // Window might already be closed
      });
    };
  }, [activePlatform]);

  return (
    <main className="flex-1 bg-bgPrimary">
      {!activePlatform && (
        <div className="flex items-center justify-center h-full text-textSecondary">
          Select a platform to start
        </div>
      )}
    </main>
  );
};
