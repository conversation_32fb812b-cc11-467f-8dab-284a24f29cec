import { useAppStore } from '../store';

const modes = ['chat', 'create', 'lab', 'hub', 'settings'] as const;

export const TopBar = () => {
  const { activeMode, setMode } = useAppStore();

  return (
    <div className="h-topbar flex items-center justify-between px-4 border-b border-border">
      <h1 className="text-xl font-semibold bg-gradient-to-r from-accentStart to-accentEnd bg-clip-text text-transparent">
        Switch.AI
      </h1>
      <nav className="flex items-center space-x-1">
        {modes.map((m) => (
          <button
            key={m}
            onClick={() => setMode(m)}
            className={`relative px-3 py-1.5 text-sm font-semibold rounded-lg transition-colors duration-200 ${
              activeMode === m ? 'text-white' : 'text-textSecondary hover:text-white'
            }`}
          >
            {m.charAt(0).toUpperCase() + m.slice(1)}
            {activeMode === m && (
              <div
                className="absolute inset-0 bg-gradient-to-r from-accentStart to-accentEnd rounded-lg -z-10"
                style={{ transition: 'left .3s, width .3s' }}
              />
            )}
          </button>
        ))}
      </nav>
      {/* This empty div helps balance the flex layout */}
      <div className="w-20"></div>
    </div>
  );
};
